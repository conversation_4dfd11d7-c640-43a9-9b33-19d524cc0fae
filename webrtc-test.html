<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <title>WebRTC + WS Signaling Test</title>
  <style>
    body { font-family: Arial, Helvetica, sans-serif; padding: 12px; }
    video { width: 45%; border: 1px solid #ccc; margin: 6px; }
    #controls { margin-top: 10px; }
    #log { white-space: pre-wrap; background:#111; color:#eee; padding:8px; height:220px; overflow:auto; }
    button { margin-right: 8px; }
    input[type=text] { width: 420px; }
  </style>
</head>
<body>
  <h2>WebRTC Test Client (WS signaling)</h2>

  <div>
    <video id="localVideo" autoplay muted playsinline></video>
    <video id="remoteVideo" autoplay playsinline></video>
  </div>

  <div id="controls">
    <button id="startBtn">Start (getUserMedia & connect)</button>
    <button id="callBtn" disabled>Call (create+send Offer)</button>
    <button id="hangupBtn" disabled>Hangup</button>
    <button id="sendDCBtn" disabled>Send DataChannel Msg</button>
    <br><br>
    <button id="closeWSBtn" disabled>Close WebSocket Only</button>
    <button id="closeWebRTCBtn" disabled>Close WebRTC Only</button>
    <label style="margin-left:12px">WS URL:
      <input id="wsUrl" type="text" value="ws://127.0.0.1:8080/api/ws" />
    </label>
  </div>

  <h4>Logs</h4>
  <div id="log"></div>

<script>
/*
 Signaling protocol (user provided):
  - Offer SDP:  8001{"type":"offer","sdp":"..."}  (can send or receive)
  - Answer SDP: 8002{"type":"answer","sdp":"..."}  (can send or receive)
  - ICE exchange: 8003{...}  (payload is candidate JSON)

 This client can now work as both caller and callee:
  - As caller: sends offer (8001), receives answer (8002)
  - As callee: receives offer (8001), sends answer (8002)
*/

const logEl = document.getElementById('log');
function log(...args){
  console.log(...args);
  logEl.textContent += `[${new Date().toLocaleTimeString()}] ` + args.map(a => (typeof a === 'object' ? JSON.stringify(a) : a)).join(' ') + '\n';
  logEl.scrollTop = logEl.scrollHeight;
}

const localVideo = document.getElementById('localVideo');
const remoteVideo = document.getElementById('remoteVideo');
const startBtn = document.getElementById('startBtn');
const callBtn = document.getElementById('callBtn');
const hangupBtn = document.getElementById('hangupBtn');
const sendDCBtn = document.getElementById('sendDCBtn');
const closeWSBtn = document.getElementById('closeWSBtn');
const closeWebRTCBtn = document.getElementById('closeWebRTCBtn');
const wsUrlInput = document.getElementById('wsUrl');

let pc = null;
let dc = null;
let localStream = null;
let ws = null;
let clientId = null; // optional if you want to set an id
let pendingCandidates = []; // candidates received before remote description is set

// TURN / ICE servers (from user)
const iceServers = [{
  urls: 'turn:************:3478',
  username: 'admin',
  credential: '123456'
}];

function createPeerConnection() {
  const config = { iceServers };
  pc = new RTCPeerConnection(config);

  pc.onicecandidate = (evt) => {
    if (evt.candidate) {
      // send candidate via websocket with prefix 8003
      const payload = {
        candidate: evt.candidate.candidate,
        sdpMid: evt.candidate.sdpMid,
        sdpMLineIndex: evt.candidate.sdpMLineIndex
      };
      sendRaw(`8003${JSON.stringify(payload)}`);
      log('Local ICE candidate sent', payload);
    } else {
      log('Local ICE gathering finished.');
    }
  };

  pc.ontrack = (evt) => {
    log('ontrack event', evt);
    // attach first stream to remote video
    if (evt.streams && evt.streams[0]) {
      remoteVideo.srcObject = evt.streams[0];
    } else {
      // fallback: create stream from tracks
      const ms = new MediaStream();
      ms.addTrack(evt.track);
      remoteVideo.srcObject = ms;
    }
  };

  pc.onconnectionstatechange = () => {
    log('ConnectionState:', pc.connectionState);
  };

  pc.ondatachannel = (event) => {
    log('Received remote DataChannel:', event.channel.label);
    const rdc = event.channel;
    rdc.onopen = () => log('Remote DC open:', rdc.label);
    rdc.onmessage = (m) => log('Remote DC message:', m.data);
    // store or use as needed
  };

  // convenience local DataChannel
  dc = pc.createDataChannel('testChannel', { ordered: true });
  dc.onopen = () => {
    log('Local DataChannel open');
    sendDCBtn.disabled = false;
    closeWebRTCBtn.disabled = false;
  };
  dc.onmessage = (evt) => log('DataChannel message:', evt.data);

  return pc;
}

function connectWebSocket() {
  const wsUrl = wsUrlInput.value.trim();
  if (!wsUrl) return alert('请填入 ws 地址');
  ws = new WebSocket(wsUrl);

  ws.onopen = () => {
    log('WebSocket connected to', wsUrl);
    callBtn.disabled = false;
    closeWSBtn.disabled = false;
    // If you want to identify yourself, you can send a login message here.
    // Example: ws.send('LOGIN:client123');
  };

  ws.onmessage = async (evt) => {
    const data = evt.data;
    log('WS recv raw:', data);
    // parse according to your prefix protocol
    if (typeof data !== 'string') {
      log('Non-string message - ignoring for this test.');
      return;
    }
    const code = data.slice(0,4);
    const payloadStr = data.slice(4);
    try {
      if (code === '8001') {
        // Received Offer SDP from server
        const obj = JSON.parse(payloadStr);
        log('Received OFFER object:', obj);
        if (!pc) createPeerConnection();

        // Ensure local media is available and added to pc
        if (!localStream) {
          log('No local stream available, getting user media...');
          await startLocalMedia();
        }

        const offer = { type: 'offer', sdp: obj.sdp };
        await pc.setRemoteDescription(offer);
        log('RemoteDescription (offer) set');
        // Create and send answer
        const answer = await pc.createAnswer();
        await pc.setLocalDescription(answer);
        log('Created local Answer');
        // send answer with prefix 8002
        const answerPayload = JSON.stringify({ type: "answer", sdp: answer.sdp });
        sendRaw('8002' + answerPayload);
        log('Answer sent via WS (8002)');
        // add any pending candidates
        for (const c of pendingCandidates) {
          try {
            await pc.addIceCandidate(c);
            log('Added pending candidate', c);
          } catch (e) {
            log('Error adding pending candidate', e);
          }
        }
        pendingCandidates = [];
      } else if (code === '8002') {
        // Answer SDP
        const obj = JSON.parse(payloadStr);
        log('Received ANSWER object:', obj);
        const answer = { type: 'answer', sdp: obj.sdp };
        await pc.setRemoteDescription(answer);
        log('RemoteDescription (answer) set');
        // add any pending candidates
        for (const c of pendingCandidates) {
          try {
            await pc.addIceCandidate(c);
            log('Added pending candidate', c);
          } catch (e) {
            log('Error adding pending candidate', e);
          }
        }
        pendingCandidates = [];
      } else if (code === '8003') {
        // ICE exchange
        const candidateObj = JSON.parse(payloadStr);
        // candidateObj should be {candidate, sdpMid, sdpMLineIndex}
        log('Received remote ICE candidate', candidateObj);
        // wrap to RTCIceCandidateInit
        const cand = {
          candidate: candidateObj.candidate,
          sdpMid: candidateObj.sdpMid,
          sdpMLineIndex: candidateObj.sdpMLineIndex
        };
        // If remote description not set yet, queue it
        if (!pc || !pc.remoteDescription) {
          pendingCandidates.push(cand);
          log('Queued candidate because remoteDescription not set');
        } else {
          try {
            await pc.addIceCandidate(cand);
            log('Added remote candidate');
          } catch (e) {
            log('Error adding remote candidate', e);
          }
        }
      } else {
        log('Unknown code:', code);
      }
    } catch (err) {
      log('Error parsing WS message payload', err);
    }
  };

  ws.onclose = () => {
    log('WebSocket closed');
    callBtn.disabled = true;
    closeWSBtn.disabled = true;
    // do not close pc automatically here; you may want to keep pc open for ICE.
  };

  ws.onerror = (e) => {
    log('WebSocket error', e);
  };
}

function sendRaw(txt) {
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.send(txt);
  } else {
    log('WS not open, cannot send:', txt);
  }
}

async function startLocalMedia() {
  try {
    localStream = await navigator.mediaDevices.getUserMedia({ audio: true, video: true });
    localVideo.srcObject = localStream;
    log('Got local stream');
    if (!pc) createPeerConnection();
    // add all tracks to pc
    for (const t of localStream.getTracks()) {
      pc.addTrack(t, localStream);
    }
    hangupBtn.disabled = false;
  } catch (err) {
    log('getUserMedia error:', err);
    alert('无法获取本地媒体: ' + err);
  }
}

async function createAndSendOffer() {
  if (!pc) createPeerConnection();
  try {
    const offer = await pc.createOffer();
    await pc.setLocalDescription(offer);
    log('Created local Offer');
    // send with prefix 8001 and payload {"type":1,"sdp":"..."}
    const payload = JSON.stringify({ type: "offer", sdp: offer.sdp });
    sendRaw('8001' + payload);
    log('Offer sent via WS (8001)');
  } catch (err) {
    log('Failed create/send offer', err);
  }
}

function hangup() {
  if (dc) {
    try { dc.close(); } catch(e){}
    dc = null;
  }
  if (pc) {
    try { pc.close(); } catch(e){}
    pc = null;
  }
  if (localStream) {
    localStream.getTracks().forEach(t => t.stop());
    localStream = null;
    localVideo.srcObject = null;
  }
  remoteVideo.srcObject = null;
  log('Hung up and cleaned local resources.');
  callBtn.disabled = false;
  hangupBtn.disabled = true;
  sendDCBtn.disabled = true;
  closeWSBtn.disabled = true;
  closeWebRTCBtn.disabled = true;
}

function closeWebSocketOnly() {
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.close();
    log('WebSocket closed manually (WebRTC connection remains active)');
  } else {
    log('WebSocket is not open');
  }
  callBtn.disabled = true;
  closeWSBtn.disabled = true;
}

function closeWebRTCOnly() {
  if (dc) {
    try { dc.close(); } catch(e){}
    dc = null;
  }
  if (pc) {
    try { pc.close(); } catch(e){}
    pc = null;
  }
  remoteVideo.srcObject = null;
  log('WebRTC connection closed manually (WebSocket remains active)');
  sendDCBtn.disabled = true;
  closeWebRTCBtn.disabled = true;
  // Keep callBtn enabled if WebSocket is still open
  if (ws && ws.readyState === WebSocket.OPEN) {
    callBtn.disabled = false;
  }
}

startBtn.onclick = async () => {
  // create pc and connect ws and getUserMedia
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    connectWebSocket();
  }
  if (!pc) createPeerConnection();
  await startLocalMedia();
  callBtn.disabled = false;
};

callBtn.onclick = async () => {
  await createAndSendOffer();
  callBtn.disabled = true;
};

hangupBtn.onclick = () => {
  hangup();
};

sendDCBtn.onclick = () => {
  if (dc && dc.readyState === 'open') {
    const msg = 'Hello from client @' + new Date().toLocaleTimeString();
    dc.send(msg);
    log('Sent via DC:', msg);
  } else {
    log('DataChannel not open');
  }
};

closeWSBtn.onclick = () => {
  closeWebSocketOnly();
};

closeWebRTCBtn.onclick = () => {
  closeWebRTCOnly();
};

// helper: close WS cleanly on unload
window.addEventListener('beforeunload', () => {
  if (ws && ws.readyState === WebSocket.OPEN) ws.close();
  if (pc) pc.close();
});
</script>
</body>
</html>

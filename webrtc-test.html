<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <title>WebRTC + WS Signaling Test</title>
  <style>
    body { font-family: Arial, Helvetica, sans-serif; padding: 12px; }
    video { width: 45%; border: 1px solid #ccc; margin: 6px; }
    #controls { margin-top: 10px; }
    #log { white-space: pre-wrap; background:#111; color:#eee; padding:8px; height:220px; overflow:auto; }
    button { margin-right: 8px; }
    input[type=text] { width: 420px; }
  </style>
</head>
<body>
  <h2>WebRTC Test Client (WS signaling)</h2>

  <div>
    <video id="localVideo" autoplay muted playsinline></video>
    <video id="remoteVideo" autoplay playsinline></video>
  </div>

  <div id="controls">
    <button id="startBtn">Start (getUserMedia & connect)</button>
    <button id="callBtn" disabled>Call (create+send Offer)</button>
    <button id="hangupBtn" disabled>Hangup</button>
    <button id="sendDCBtn" disabled>Send DataChannel Msg</button>
    <br><br>
    <button id="closeWSBtn" disabled>Close WebSocket Only</button>
    <button id="closeWebRTCBtn" disabled>Close WebRTC Only</button>
    <br><br>
    <label>Upload Audio File:
      <input id="audioFileInput" type="file" accept="audio/*" />
    </label>
    <button id="sendAudioBtn" disabled>Send Audio via DataChannel</button>
    <button id="playAudioTrackBtn" disabled>Send Audio via Track</button>
    <button id="stopAudioTrackBtn" disabled>Stop Audio Track</button>
    <button id="debugTracksBtn" disabled>Debug: Show Tracks</button>
    <br>
    <label>Audio Volume:
      <input id="audioVolumeSlider" type="range" min="0.1" max="5.0" step="0.1" value="2.0" disabled />
      <span id="volumeValue">2.0x</span>
    </label>
    <div id="audioInfo" style="margin-top: 8px; font-size: 12px; color: #666;"></div>
    <audio id="audioPlayer" controls style="display: none;"></audio>
    <label style="margin-left:12px">WS URL:
      <input id="wsUrl" type="text" value="ws://127.0.0.1:8080/api/ws" />
    </label>
  </div>

  <h4>Logs</h4>
  <div id="log"></div>

<script>
/*
 Signaling protocol (user provided):
  - Offer SDP:  8001{"type":"offer","sdp":"..."}  (can send or receive)
  - Answer SDP: 8002{"type":"answer","sdp":"..."}  (can send or receive)
  - ICE exchange: 8003{...}  (payload is candidate JSON)

 This client can now work as both caller and callee:
  - As caller: sends offer (8001), receives answer (8002)
  - As callee: receives offer (8001), sends answer (8002)
*/

const logEl = document.getElementById('log');
function log(...args){
  console.log(...args);
  logEl.textContent += `[${new Date().toLocaleTimeString()}] ` + args.map(a => (typeof a === 'object' ? JSON.stringify(a) : a)).join(' ') + '\n';
  logEl.scrollTop = logEl.scrollHeight;
}

const localVideo = document.getElementById('localVideo');
const remoteVideo = document.getElementById('remoteVideo');
const startBtn = document.getElementById('startBtn');
const callBtn = document.getElementById('callBtn');
const hangupBtn = document.getElementById('hangupBtn');
const sendDCBtn = document.getElementById('sendDCBtn');
const closeWSBtn = document.getElementById('closeWSBtn');
const closeWebRTCBtn = document.getElementById('closeWebRTCBtn');
const audioFileInput = document.getElementById('audioFileInput');
const sendAudioBtn = document.getElementById('sendAudioBtn');
const playAudioTrackBtn = document.getElementById('playAudioTrackBtn');
const stopAudioTrackBtn = document.getElementById('stopAudioTrackBtn');
const debugTracksBtn = document.getElementById('debugTracksBtn');
const audioVolumeSlider = document.getElementById('audioVolumeSlider');
const volumeValue = document.getElementById('volumeValue');
const audioInfo = document.getElementById('audioInfo');
const audioPlayer = document.getElementById('audioPlayer');
const wsUrlInput = document.getElementById('wsUrl');

let pc = null;
let dc = null;
let localStream = null;
let ws = null;
let clientId = null; // optional if you want to set an id
let pendingCandidates = []; // candidates received before remote description is set
let selectedAudioFile = null; // store selected audio file
let audioFileData = null; // store audio file as ArrayBuffer
let audioFileStream = null; // MediaStream from audio file
let audioFileSender = null; // RTCRtpSender for audio file track
let audioContext = null; // AudioContext for audio processing
let audioSource = null; // MediaElementSourceNode
let audioGainNode = null; // GainNode for volume control

// TURN / ICE servers (from user)
const iceServers = [{
  urls: 'turn:************:3478',
  username: 'admin',
  credential: '123456'
}];

function createPeerConnection() {
  const config = {
    iceServers,
    // 禁用音频处理以保持原始音质
    sdpSemantics: 'unified-plan'
  };
  pc = new RTCPeerConnection(config);

  pc.onicecandidate = (evt) => {
    if (evt.candidate) {
      // send candidate via websocket with prefix 8003
      const payload = {
        candidate: evt.candidate.candidate,
        sdpMid: evt.candidate.sdpMid,
        sdpMLineIndex: evt.candidate.sdpMLineIndex
      };
      sendRaw(`8003${JSON.stringify(payload)}`);
      log('Local ICE candidate sent', payload);
    } else {
      log('Local ICE gathering finished.');
    }
  };

  pc.ontrack = (evt) => {
    log('ontrack event', evt);
    // attach first stream to remote video
    if (evt.streams && evt.streams[0]) {
      remoteVideo.srcObject = evt.streams[0];
    } else {
      // fallback: create stream from tracks
      const ms = new MediaStream();
      ms.addTrack(evt.track);
      remoteVideo.srcObject = ms;
    }
  };

  pc.onconnectionstatechange = () => {
    log('ConnectionState:', pc.connectionState);
    // Enable audio track button when connected and file is loaded
    if (pc.connectionState === 'connected' && selectedAudioFile) {
      playAudioTrackBtn.disabled = false;
    } else {
      playAudioTrackBtn.disabled = true;
    }
  };

  pc.ondatachannel = (event) => {
    log('Received remote DataChannel:', event.channel.label);
    const rdc = event.channel;
    rdc.onopen = () => log('Remote DC open:', rdc.label);
    rdc.onmessage = (m) => log('Remote DC message:', m.data);
    // store or use as needed
  };

  // convenience local DataChannel
  dc = pc.createDataChannel('testChannel', { ordered: true });
  dc.onopen = () => {
    log('Local DataChannel open');
    sendDCBtn.disabled = false;
    closeWebRTCBtn.disabled = false;
    // Enable audio send button if file is loaded
    if (audioFileData) {
      sendAudioBtn.disabled = false;
    }
  };
  dc.onmessage = (evt) => log('DataChannel message:', evt.data);

  return pc;
}

function connectWebSocket() {
  const wsUrl = wsUrlInput.value.trim();
  if (!wsUrl) return alert('请填入 ws 地址');
  ws = new WebSocket(wsUrl);

  ws.onopen = () => {
    log('WebSocket connected to', wsUrl);
    callBtn.disabled = false;
    closeWSBtn.disabled = false;
    // If you want to identify yourself, you can send a login message here.
    // Example: ws.send('LOGIN:client123');
  };

  ws.onmessage = async (evt) => {
    const data = evt.data;
    log('WS recv raw:', data);
    // parse according to your prefix protocol
    if (typeof data !== 'string') {
      log('Non-string message - ignoring for this test.');
      return;
    }
    const code = data.slice(0,4);
    const payloadStr = data.slice(4);
    try {
      if (code === '8001') {
        // Received Offer SDP from server
        const obj = JSON.parse(payloadStr);
        log('Received OFFER object:', obj);
        if (!pc) createPeerConnection();

        // Ensure local media is available and added to pc
        if (!localStream) {
          log('No local stream available, getting user media...');
          await startLocalMedia();
        } else {
          // Make sure tracks are added to pc if not already
          const existingSenders = pc.getSenders();
          for (const track of localStream.getTracks()) {
            const existingSender = existingSenders.find(sender => sender.track === track);
            if (!existingSender) {
              pc.addTrack(track, localStream);
              log('Added missing track to PeerConnection:', track.kind);
            }
          }
        }

        const offer = { type: 'offer', sdp: obj.sdp };
        await pc.setRemoteDescription(offer);
        log('RemoteDescription (offer) set');
        // Create and send answer
        const answer = await pc.createAnswer();
        await pc.setLocalDescription(answer);
        log('Created local Answer');
        // send answer with prefix 8002
        const answerPayload = JSON.stringify({ type: "answer", sdp: answer.sdp });
        sendRaw('8002' + answerPayload);
        log('Answer sent via WS (8002)');
        // add any pending candidates
        for (const c of pendingCandidates) {
          try {
            await pc.addIceCandidate(c);
            log('Added pending candidate', c);
          } catch (e) {
            log('Error adding pending candidate', e);
          }
        }
        pendingCandidates = [];
      } else if (code === '8002') {
        // Answer SDP
        const obj = JSON.parse(payloadStr);
        log('Received ANSWER object:', obj);
        const answer = { type: 'answer', sdp: obj.sdp };
        await pc.setRemoteDescription(answer);
        log('RemoteDescription (answer) set');
        // add any pending candidates
        for (const c of pendingCandidates) {
          try {
            await pc.addIceCandidate(c);
            log('Added pending candidate', c);
          } catch (e) {
            log('Error adding pending candidate', e);
          }
        }
        pendingCandidates = [];
      } else if (code === '8003') {
        // ICE exchange
        const candidateObj = JSON.parse(payloadStr);
        // candidateObj should be {candidate, sdpMid, sdpMLineIndex}
        log('Received remote ICE candidate', candidateObj);
        // wrap to RTCIceCandidateInit
        const cand = {
          candidate: candidateObj.candidate,
          sdpMid: candidateObj.sdpMid,
          sdpMLineIndex: candidateObj.sdpMLineIndex
        };
        // If remote description not set yet, queue it
        if (!pc || !pc.remoteDescription) {
          pendingCandidates.push(cand);
          log('Queued candidate because remoteDescription not set');
        } else {
          try {
            await pc.addIceCandidate(cand);
            log('Added remote candidate');
          } catch (e) {
            log('Error adding remote candidate', e);
          }
        }
      } else {
        log('Unknown code:', code);
      }
    } catch (err) {
      log('Error parsing WS message payload', err);
    }
  };

  ws.onclose = () => {
    log('WebSocket closed');
    callBtn.disabled = true;
    closeWSBtn.disabled = true;
    // do not close pc automatically here; you may want to keep pc open for ICE.
  };

  ws.onerror = (e) => {
    log('WebSocket error', e);
  };
}

function sendRaw(txt) {
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.send(txt);
  } else {
    log('WS not open, cannot send:', txt);
  }
}

async function startLocalMedia() {
  try {
    localStream = await navigator.mediaDevices.getUserMedia({ audio: true, video: true });
    localVideo.srcObject = localStream;
    log('Got local stream');
    if (!pc) createPeerConnection();
    // add all tracks to pc
    for (const t of localStream.getTracks()) {
      pc.addTrack(t, localStream);
      log('Added local track to PeerConnection:', t.kind, t.id);
    }
    hangupBtn.disabled = false;
    debugTracksBtn.disabled = false;
  } catch (err) {
    log('getUserMedia error:', err);
    alert('无法获取本地媒体: ' + err);
  }
}

async function createAndSendOffer() {
  if (!pc) createPeerConnection();
  try {
    const offer = await pc.createOffer();
    await pc.setLocalDescription(offer);
    log('Created local Offer');
    // send with prefix 8001 and payload {"type":1,"sdp":"..."}
    const payload = JSON.stringify({ type: "offer", sdp: offer.sdp });
    sendRaw('8001' + payload);
    log('Offer sent via WS (8001)');
  } catch (err) {
    log('Failed create/send offer', err);
  }
}

async function renegotiate() {
  if (!pc) {
    log('No PeerConnection for renegotiation');
    return;
  }

  try {
    log('Starting renegotiation...');
    const offer = await pc.createOffer();
    await pc.setLocalDescription(offer);
    log('Created renegotiation offer');

    // Send the new offer
    const payload = JSON.stringify({ type: "offer", sdp: offer.sdp });
    sendRaw('8001' + payload);
    log('Renegotiation offer sent via WS (8001)');
  } catch (err) {
    log('Failed to renegotiate', err);
  }
}

async function hangup() {
  // Stop audio file playback if active
  if (audioFileSender || audioFileStream) {
    await stopAudioTrack();
  }

  if (dc) {
    try { dc.close(); } catch(e){}
    dc = null;
  }
  if (pc) {
    try { pc.close(); } catch(e){}
    pc = null;
  }
  if (localStream) {
    localStream.getTracks().forEach(t => t.stop());
    localStream = null;
    localVideo.srcObject = null;
  }
  remoteVideo.srcObject = null;
  log('Hung up and cleaned local resources.');
  callBtn.disabled = false;
  hangupBtn.disabled = true;
  sendDCBtn.disabled = true;
  sendAudioBtn.disabled = true;
  playAudioTrackBtn.disabled = true;
  stopAudioTrackBtn.disabled = true;
  debugTracksBtn.disabled = true;
  closeWSBtn.disabled = true;
  closeWebRTCBtn.disabled = true;
}

function closeWebSocketOnly() {
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.close();
    log('WebSocket closed manually (WebRTC connection remains active)');
  } else {
    log('WebSocket is not open');
  }
  callBtn.disabled = true;
  closeWSBtn.disabled = true;
}

async function closeWebRTCOnly() {
  // Stop audio file playback if active
  if (audioFileSender || audioFileStream) {
    await stopAudioTrack();
  }

  if (dc) {
    try { dc.close(); } catch(e){}
    dc = null;
  }
  if (pc) {
    try { pc.close(); } catch(e){}
    pc = null;
  }
  remoteVideo.srcObject = null;
  log('WebRTC connection closed manually (WebSocket remains active)');
  sendDCBtn.disabled = true;
  sendAudioBtn.disabled = true;
  playAudioTrackBtn.disabled = true;
  stopAudioTrackBtn.disabled = true;
  debugTracksBtn.disabled = true;
  closeWebRTCBtn.disabled = true;
  // Keep callBtn enabled if WebSocket is still open
  if (ws && ws.readyState === WebSocket.OPEN) {
    callBtn.disabled = false;
  }
}

async function handleAudioFileSelect(event) {
  const file = event.target.files[0];
  if (!file) {
    selectedAudioFile = null;
    audioFileData = null;
    audioInfo.textContent = '';
    sendAudioBtn.disabled = true;
    return;
  }

  selectedAudioFile = file;
  audioInfo.textContent = `Selected: ${file.name} (${(file.size / 1024).toFixed(1)} KB, ${file.type})`;
  log('Audio file selected:', file.name, file.size, 'bytes');

  // 检测音频文件的属性（如果可能）
  if (file.type.includes('audio')) {
    log('Audio file type detected - optimizing for 16kHz mono audio');
  }

  try {
    // Read file as ArrayBuffer
    audioFileData = await file.arrayBuffer();
    log('Audio file loaded into memory:', audioFileData.byteLength, 'bytes');

    // Enable send buttons if connections are ready
    if (dc && dc.readyState === 'open') {
      sendAudioBtn.disabled = false;
    }
    if (pc && pc.connectionState === 'connected') {
      playAudioTrackBtn.disabled = false;
    }
  } catch (err) {
    log('Error reading audio file:', err);
    audioInfo.textContent = 'Error reading file: ' + err.message;
    sendAudioBtn.disabled = true;
  }
}

async function sendAudioViaDataChannel() {
  if (!audioFileData) {
    log('No audio file data available');
    return;
  }

  if (!dc || dc.readyState !== 'open') {
    log('DataChannel not open');
    return;
  }

  try {
    const chunkSize = 16384; // 16KB chunks
    const totalSize = audioFileData.byteLength;
    const totalChunks = Math.ceil(totalSize / chunkSize);

    log(`Sending audio file: ${selectedAudioFile.name}, ${totalSize} bytes in ${totalChunks} chunks`);

    // Send metadata first
    const metadata = {
      type: 'audio_start',
      filename: selectedAudioFile.name,
      fileType: selectedAudioFile.type,
      totalSize: totalSize,
      totalChunks: totalChunks,
      chunkSize: chunkSize
    };
    dc.send(JSON.stringify(metadata));
    log('Sent audio metadata:', metadata);

    // Send file data in chunks
    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, totalSize);
      const chunk = audioFileData.slice(start, end);

      // Create chunk header
      const chunkHeader = {
        type: 'audio_chunk',
        chunkIndex: i,
        chunkSize: chunk.byteLength
      };

      // Send header first, then binary data
      dc.send(JSON.stringify(chunkHeader));
      dc.send(chunk);

      log(`Sent chunk ${i + 1}/${totalChunks} (${chunk.byteLength} bytes)`);

      // Small delay to avoid overwhelming the channel
      if (i < totalChunks - 1) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }

    // Send completion signal
    const completion = {
      type: 'audio_complete',
      filename: selectedAudioFile.name
    };
    dc.send(JSON.stringify(completion));
    log('Audio file transmission completed');

  } catch (err) {
    log('Error sending audio file:', err);
  }
}

async function playAudioAsTrack() {
  if (!selectedAudioFile) {
    log('No audio file selected');
    return;
  }

  if (!pc || pc.connectionState !== 'connected') {
    log('PeerConnection not connected');
    return;
  }

  try {
    // Clean up any existing audio context and source
    await cleanupAudioContext();

    // Create new audio element for this playback to avoid reuse issues
    const newAudioPlayer = document.createElement('audio');
    newAudioPlayer.controls = true;
    newAudioPlayer.style.display = 'none';
    document.body.appendChild(newAudioPlayer);

    // Create URL for the audio file
    const audioUrl = URL.createObjectURL(selectedAudioFile);
    newAudioPlayer.src = audioUrl;

    // Wait for audio to load metadata
    await new Promise((resolve, reject) => {
      newAudioPlayer.onloadedmetadata = resolve;
      newAudioPlayer.onerror = reject;
      newAudioPlayer.load();
    });

    log('Audio file loaded, creating MediaStream...');

    // Create new AudioContext optimized for 16kHz mono audio
    audioContext = new (window.AudioContext || window.webkitAudioContext)({
      sampleRate: 16000  // 匹配你的音频采样率
    });
    audioSource = audioContext.createMediaElementSource(newAudioPlayer);
    const destination = audioContext.createMediaStreamDestination();

    // 创建增益节点来提高音量
    audioGainNode = audioContext.createGain();
    audioGainNode.gain.value = parseFloat(audioVolumeSlider.value); // 使用滑块值

    // 创建压缩器来减少动态范围，保持音质
    const compressor = audioContext.createDynamicsCompressor();
    compressor.threshold.value = -24;
    compressor.knee.value = 30;
    compressor.ratio.value = 12;
    compressor.attack.value = 0.003;
    compressor.release.value = 0.25;

    // Connect audio processing chain
    audioSource.connect(audioGainNode);
    audioGainNode.connect(compressor);
    compressor.connect(destination);
    // Also connect to audio context destination so we can hear it locally
    compressor.connect(audioContext.destination);

    // 启用音量滑块
    audioVolumeSlider.disabled = false;

    log('Created audio processing chain with gain and compression');

    audioFileStream = destination.stream;
    const audioTrack = audioFileStream.getAudioTracks()[0];

    if (!audioTrack) {
      throw new Error('No audio track found in stream');
    }

    log('Created audio track from file:', audioTrack.label);

    // 显示音频轨道的详细信息
    const settings = audioTrack.getSettings();
    log('Audio track settings:', {
      sampleRate: settings.sampleRate,
      channelCount: settings.channelCount,
      sampleSize: settings.sampleSize,
      echoCancellation: settings.echoCancellation,
      noiseSuppression: settings.noiseSuppression,
      autoGainControl: settings.autoGainControl
    });

    // Add track to peer connection (replace existing audio track if any)
    const existingAudioSender = pc.getSenders().find(sender =>
      sender.track && sender.track.kind === 'audio'
    );

    if (existingAudioSender) {
      // Replace existing audio track
      await existingAudioSender.replaceTrack(audioTrack);
      audioFileSender = existingAudioSender;
      log('Replaced existing audio track with file audio');

      // 优化16kHz单声道音频的编码参数
      try {
        const params = existingAudioSender.getParameters();
        if (params.encodings && params.encodings.length > 0) {
          // 针对16kHz单声道音频优化比特率
          params.encodings[0].maxBitrate = 64000; // 64 kbps 对16kHz单声道足够
          params.encodings[0].priority = 'high';
        }
        await existingAudioSender.setParameters(params);
        log('Updated sender parameters for 16kHz mono audio');
      } catch (e) {
        log('Could not update sender parameters:', e);
      }
    } else {
      // Add new audio track
      audioFileSender = pc.addTrack(audioTrack, audioFileStream);
      log('Added new audio track from file');

      // 设置16kHz单声道音频的发送参数
      try {
        const params = audioFileSender.getParameters();
        if (params.encodings && params.encodings.length > 0) {
          // 针对16kHz单声道音频优化比特率
          params.encodings[0].maxBitrate = 64000; // 64 kbps 对16kHz单声道足够
          params.encodings[0].priority = 'high';
        }
        await audioFileSender.setParameters(params);
        log('Set sender parameters for 16kHz mono audio');
      } catch (e) {
        log('Could not set sender parameters:', e);
      }

      // Need to renegotiate when adding new track
      log('Renegotiating after adding new track...');
      await renegotiate();
    }

    // Start playing the audio
    await newAudioPlayer.play();
    log('Started playing audio file via WebRTC track');

    playAudioTrackBtn.disabled = true;
    stopAudioTrackBtn.disabled = false;

    // Store reference to the audio element for cleanup
    newAudioPlayer._isFileAudio = true;

    // Handle audio end
    newAudioPlayer.onended = () => {
      log('Audio file playback ended');
      stopAudioTrack();
    };

  } catch (err) {
    log('Error playing audio as track:', err);
    alert('Error playing audio: ' + err.message);
    await cleanupAudioContext();
  }
}

async function cleanupAudioContext() {
  try {
    // Clean up audio context and source
    if (audioSource) {
      audioSource.disconnect();
      audioSource = null;
    }

    if (audioGainNode) {
      audioGainNode.disconnect();
      audioGainNode = null;
    }

    if (audioContext && audioContext.state !== 'closed') {
      await audioContext.close();
      audioContext = null;
    }

    // 禁用音量滑块
    audioVolumeSlider.disabled = true;

    // Clean up any file audio elements
    const fileAudioElements = document.querySelectorAll('audio[_isFileAudio]');
    fileAudioElements.forEach(element => {
      if (element.src) {
        element.pause();
        URL.revokeObjectURL(element.src);
      }
      element.remove();
    });

    if (audioFileStream) {
      audioFileStream.getTracks().forEach(track => track.stop());
      audioFileStream = null;
    }

  } catch (err) {
    log('Error cleaning up audio context:', err);
  }
}

function debugShowTracks() {
  if (!pc) {
    log('No PeerConnection available');
    return;
  }

  log('=== DEBUG: Current PeerConnection Senders ===');
  const senders = pc.getSenders();
  senders.forEach((sender, index) => {
    if (sender.track) {
      const settings = sender.track.getSettings();
      log(`Sender ${index}: ${sender.track.kind} track, ID: ${sender.track.id}, enabled: ${sender.track.enabled}, readyState: ${sender.track.readyState}`);
      if (sender.track.kind === 'audio') {
        log(`  Audio settings: sampleRate=${settings.sampleRate}, channels=${settings.channelCount}, sampleSize=${settings.sampleSize}`);
        log(`  Audio processing: echo=${settings.echoCancellation}, noise=${settings.noiseSuppression}, agc=${settings.autoGainControl}`);
      }
    } else {
      log(`Sender ${index}: No track`);
    }
  });

  log('=== DEBUG: Local Stream Tracks ===');
  if (localStream) {
    localStream.getTracks().forEach((track, index) => {
      log(`Local Track ${index}: ${track.kind}, ID: ${track.id}, enabled: ${track.enabled}, readyState: ${track.readyState}`);
    });
  } else {
    log('No local stream');
  }

  log('=== DEBUG: Audio File Stream Tracks ===');
  if (audioFileStream) {
    audioFileStream.getTracks().forEach((track, index) => {
      log(`Audio File Track ${index}: ${track.kind}, ID: ${track.id}, enabled: ${track.enabled}, readyState: ${track.readyState}`);
    });
  } else {
    log('No audio file stream');
  }

  log('=== DEBUG: Connection State ===');
  log(`Connection State: ${pc.connectionState}`);
  log(`ICE Connection State: ${pc.iceConnectionState}`);
  log(`Signaling State: ${pc.signalingState}`);
}

async function stopAudioTrack() {
  try {
    if (audioFileSender && audioFileSender.track) {
      // Stop the audio track
      audioFileSender.track.stop();
      log('Stopped audio file track');
    }

    // Clean up audio context and elements
    await cleanupAudioContext();

    // Restore original microphone audio if available
    if (localStream && audioFileSender) {
      const micAudioTrack = localStream.getAudioTracks()[0];
      if (micAudioTrack) {
        await audioFileSender.replaceTrack(micAudioTrack);
        log('Restored microphone audio track');
      }
    }

    audioFileSender = null;
    playAudioTrackBtn.disabled = false;
    stopAudioTrackBtn.disabled = true;

    log('Audio track playback stopped');

  } catch (err) {
    log('Error stopping audio track:', err);
  }
}

startBtn.onclick = async () => {
  // create pc and connect ws and getUserMedia
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    connectWebSocket();
  }
  if (!pc) createPeerConnection();
  await startLocalMedia();
  callBtn.disabled = false;
};

callBtn.onclick = async () => {
  await createAndSendOffer();
  callBtn.disabled = true;
};

hangupBtn.onclick = async () => {
  await hangup();
};

sendDCBtn.onclick = () => {
  if (dc && dc.readyState === 'open') {
    const msg = 'Hello from client @' + new Date().toLocaleTimeString();
    dc.send(msg);
    log('Sent via DC:', msg);
  } else {
    log('DataChannel not open');
  }
};

closeWSBtn.onclick = () => {
  closeWebSocketOnly();
};

closeWebRTCBtn.onclick = async () => {
  await closeWebRTCOnly();
};

audioFileInput.onchange = handleAudioFileSelect;

sendAudioBtn.onclick = () => {
  sendAudioViaDataChannel();
};

playAudioTrackBtn.onclick = () => {
  playAudioAsTrack();
};

stopAudioTrackBtn.onclick = async () => {
  await stopAudioTrack();
};

debugTracksBtn.onclick = () => {
  debugShowTracks();
};

audioVolumeSlider.oninput = () => {
  const volume = parseFloat(audioVolumeSlider.value);
  volumeValue.textContent = volume.toFixed(1) + 'x';

  // 实时更新增益节点的音量
  if (audioGainNode) {
    audioGainNode.gain.value = volume;
    log('Updated audio volume to', volume);
  }
};

// helper: close WS cleanly on unload
window.addEventListener('beforeunload', () => {
  if (ws && ws.readyState === WebSocket.OPEN) ws.close();
  if (pc) pc.close();
});
</script>
</body>
</html>
